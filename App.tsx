/**
 * 智能社交电商App主入口
 * 集成主题系统和UI组件库
 */

import React, { useEffect } from 'react';
import { StatusBar } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { initializeDebugTools } from './src/config/debug';
import { Logger } from './src/config/logger';
import ThemeDemo from './src/screens/ThemeDemo';
import { ThemeProvider, useTheme } from './src/theme/ThemeContext';

// 调试工具初始化
if (__DEV__) {
  // 初始化Reactotron
  // require('./src/config/reactotron');
  // 初始化调试工具
  // require('./src/config/debug');
}

// 应用内容组件
const AppContent: React.FC = () => {
  const { theme, isDark } = useTheme();

  useEffect(() => {
    // 初始化调试工具
    initializeDebugTools();
    Logger.info('App started with theme system', { isDark });

    // 设置状态栏样式
    StatusBar.setBarStyle(isDark ? 'light-content' : 'dark-content', true);
  }, [isDark]);

  return (
    <>
      <StatusBar
        barStyle={isDark ? 'light-content' : 'dark-content'}
        backgroundColor={theme.colors.surface}
        translucent={false}
      />
      <ThemeDemo />
    </>
  );
};

// 主应用组件
const App: React.FC = () => (
  <SafeAreaProvider>
    <ThemeProvider>
      <AppContent />
    </ThemeProvider>
  </SafeAreaProvider>
);

export default App;
