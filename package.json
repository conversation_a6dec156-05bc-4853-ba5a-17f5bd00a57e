{"name": "SmartSocialEcommerceApp", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "lint:check": "eslint . --max-warnings 20", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "validate": "npm run type-check && npm run lint:check && npm run format:check && npm run test", "clean": "rm -rf node_modules && npm install", "clean:cache": "npm start -- --reset-cache", "clean:ios": "cd ios && xcodebuild clean && cd ..", "clean:android": "cd android && ./gradlew clean && cd ..", "bundle:analyze": "npx react-native-bundle-visualizer", "bundle:analyze:android": "npx react-native-bundle-visualizer --platform android", "bundle:analyze:ios": "npx react-native-bundle-visualizer --platform ios", "commit": "cz", "commitlint": "commitlint --edit", "prepare": "husky"}, "dependencies": {"@sentry/react-native": "^6.19.0", "react": "18.3.1", "react-native": "0.76.1", "react-native-image-colors": "2.4.0", "react-native-logs": "^5.3.0", "react-native-mmkv": "^2.12.2", "react-native-paper": "5.11.6", "react-native-safe-area-context": "^5.5.2", "react-native-vector-icons": "10.0.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@react-native-community/cli": "15.0.0", "@react-native-community/cli-platform-android": "15.0.0", "@react-native-community/cli-platform-ios": "15.0.0", "@react-native/babel-preset": "0.76.1", "@react-native/eslint-config": "0.76.1", "@react-native/metro-config": "0.76.1", "@react-native/typescript-config": "0.76.1", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.19.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "husky": "^9.1.7", "jest": "^29.6.3", "lint-staged": "^16.1.4", "prettier": "^3.6.2", "react-native-bundle-visualizer": "^3.1.3", "react-test-renderer": "18.3.1", "reactotron-react-native": "^5.1.14", "reactotron-redux": "^3.2.0", "reactotron-redux-saga": "^4.2.3", "typescript": "^5.5.3"}, "engines": {"node": ">=18"}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}}